from flask import Flask, render_template, request, jsonify, send_file
from flask_socketio import Socket<PERSON>, emit
import tushare as ts
import pandas as pd
from datetime import datetime, timedelta
import time
import os
import threading
import json

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'
socketio = SocketIO(app, cors_allowed_origins="*")

# 设置tushare token
ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
pro = ts.pro_api()

# 全局变量
download_status = {
    'is_running': False,
    'current_segment': 0,
    'total_segments': 0,
    'total_records': 0,
    'progress': 0,
    'status': 'waiting',
    'error': None
}

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/start_download', methods=['POST'])
def start_download():
    global download_status
    
    if download_status['is_running']:
        return jsonify({'error': '下载正在进行中'}), 400
    
    data = request.json
    start_date = data.get('start_date')
    end_date = data.get('end_date')
    
    if not start_date or not end_date:
        return jsonify({'error': '请提供开始日期和结束日期'}), 400
    
    # 重置状态
    download_status.update({
        'is_running': True,
        'current_segment': 0,
        'total_segments': 0,
        'total_records': 0,
        'progress': 0,
        'status': 'starting',
        'error': None
    })
    
    # 在后台线程中开始下载
    thread = threading.Thread(target=download_data_background, args=(start_date, end_date))
    thread.daemon = True
    thread.start()
    
    return jsonify({'message': '下载已开始'})

@app.route('/api/stop_download', methods=['POST'])
def stop_download():
    global download_status
    download_status['is_running'] = False
    download_status['status'] = 'stopped'
    
    socketio.emit('download_status', download_status)
    return jsonify({'message': '下载已停止'})

@app.route('/api/download_status')
def get_download_status():
    return jsonify(download_status)

@app.route('/api/download_history')
def get_download_history():
    # 从文件或数据库读取下载历史
    history_file = 'download_history.json'
    if os.path.exists(history_file):
        with open(history_file, 'r', encoding='utf-8') as f:
            history = json.load(f)
    else:
        history = []
    
    return jsonify(history)

@app.route('/api/download_file/<filename>')
def download_file(filename):
    try:
        return send_file(filename, as_attachment=True)
    except FileNotFoundError:
        return jsonify({'error': '文件不存在'}), 404

def split_date_range(start_date, end_date, days_per_chunk=30):
    """将日期范围分割成小段"""
    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    end_dt = datetime.strptime(end_date, '%Y-%m-%d')
    
    date_ranges = []
    current_start = start_dt
    
    while current_start <= end_dt:
        current_end = min(current_start + timedelta(days=days_per_chunk-1), end_dt)
        date_ranges.append((
            current_start.strftime('%Y%m%d'),
            current_end.strftime('%Y%m%d')
        ))
        current_start = current_end + timedelta(days=1)
    
    return date_ranges

def download_daily_basic_segment(start_date, end_date):
    """下载指定日期范围的daily_basic数据"""
    df = None
    page = 1
    max_offset = 99000
    
    while download_status['is_running']:
        try:
            current_offset = (page-1) * 1000
            if current_offset >= max_offset:
                break
                
            temp_df = pro.daily_basic(
                ts_code='',
                start_date=start_date,
                end_date=end_date,
                limit=1000,
                offset=current_offset
            )
            
            if temp_df.empty:
                break
            
            if df is None:
                df = temp_df
            else:
                df = pd.concat([df, temp_df], ignore_index=True)
            
            page += 1
            time.sleep(0.3)
            
        except Exception as e:
            if "offset不能大于" in str(e):
                break
            else:
                raise e
    
    return df if df is not None else pd.DataFrame()

def download_data_background(start_date, end_date):
    """后台下载数据"""
    global download_status
    
    try:
        # 计算分段
        date_ranges = split_date_range(start_date, end_date, days_per_chunk=30)
        download_status['total_segments'] = len(date_ranges)
        download_status['status'] = 'downloading'
        
        socketio.emit('download_log', {
            'message': f'开始下载 {start_date} 至 {end_date} 的数据',
            'type': 'info'
        })
        
        socketio.emit('download_log', {
            'message': f'总共需要下载 {len(date_ranges)} 个时间段',
            'type': 'info'
        })
        
        all_data = []
        
        for i, (seg_start, seg_end) in enumerate(date_ranges, 1):
            if not download_status['is_running']:
                break
                
            download_status['current_segment'] = i
            download_status['progress'] = (i / len(date_ranges)) * 100
            
            socketio.emit('download_status', download_status)
            socketio.emit('download_log', {
                'message': f'正在下载第{i}/{len(date_ranges)}段 ({seg_start} 至 {seg_end})',
                'type': 'info'
            })
            
            try:
                segment_df = download_daily_basic_segment(seg_start, seg_end)
                
                if not segment_df.empty:
                    all_data.append(segment_df)
                    download_status['total_records'] += len(segment_df)
                    
                    socketio.emit('download_log', {
                        'message': f'✓ 第{i}/{len(date_ranges)}段 完成，获得 {len(segment_df):,} 条记录',
                        'type': 'success'
                    })
                else:
                    socketio.emit('download_log', {
                        'message': f'- 第{i}/{len(date_ranges)}段 无数据',
                        'type': 'warning'
                    })
                
                socketio.emit('download_status', download_status)
                time.sleep(1)
                
            except Exception as e:
                socketio.emit('download_log', {
                    'message': f'✗ 第{i}/{len(date_ranges)}段 失败：{str(e)}',
                    'type': 'error'
                })
                download_status['error'] = str(e)
                break
        
        if download_status['is_running'] and all_data:
            # 合并数据
            socketio.emit('download_log', {
                'message': f'正在合并 {len(all_data)} 个数据段...',
                'type': 'info'
            })
            
            df = pd.concat(all_data, ignore_index=True)
            
            # 保存文件
            filename = f'daily_basic_{start_date.replace("-", "")}_{end_date.replace("-", "")}.csv'
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            
            # 保存下载历史
            save_download_history(start_date, end_date, len(df), filename)
            
            download_status['status'] = 'completed'
            download_status['progress'] = 100
            
            socketio.emit('download_log', {
                'message': f'下载完成！总计 {len(df):,} 条记录，已保存至 {filename}',
                'type': 'success'
            })
        
    except Exception as e:
        download_status['error'] = str(e)
        download_status['status'] = 'error'
        socketio.emit('download_log', {
            'message': f'下载失败：{str(e)}',
            'type': 'error'
        })
    
    finally:
        download_status['is_running'] = False
        socketio.emit('download_status', download_status)

def save_download_history(start_date, end_date, record_count, filename):
    """保存下载历史"""
    history_file = 'download_history.json'
    
    # 读取现有历史
    if os.path.exists(history_file):
        with open(history_file, 'r', encoding='utf-8') as f:
            history = json.load(f)
    else:
        history = []
    
    # 添加新记录
    new_record = {
        'download_time': datetime.now().isoformat(),
        'start_date': start_date,
        'end_date': end_date,
        'record_count': record_count,
        'filename': filename,
        'file_size': os.path.getsize(filename) if os.path.exists(filename) else 0,
        'status': 'completed'
    }
    
    history.insert(0, new_record)  # 最新的在前面
    
    # 只保留最近50条记录
    history = history[:50]
    
    # 保存历史
    with open(history_file, 'w', encoding='utf-8') as f:
        json.dump(history, f, ensure_ascii=False, indent=2)

@socketio.on('connect')
def handle_connect():
    emit('download_status', download_status)

if __name__ == '__main__':
    # 创建templates目录并移动HTML文件
    os.makedirs('templates', exist_ok=True)
    if os.path.exists('index.html'):
        import shutil
        shutil.move('index.html', 'templates/index.html')
    
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
