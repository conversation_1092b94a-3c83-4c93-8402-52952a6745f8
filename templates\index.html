<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tushare Daily Basic 数据下载工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-graph-up"></i>
                Tushare 数据下载工具
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="https://tushare.pro/" target="_blank">
                    <i class="bi bi-box-arrow-up-right"></i>
                    Tushare官网
                </a>
                <a class="nav-link" href="https://tushare.pro/document/2?doc_id=32" target="_blank">
                    <i class="bi bi-book"></i>
                    Daily Basic文档
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 下载控制区域 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-download"></i>
                            数据下载控制
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="startDate" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="startDate">
                            </div>
                            <div class="col-md-3">
                                <label for="endDate" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="endDate">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button class="btn btn-success" id="startDownload">
                                        <i class="bi bi-play-fill"></i>
                                        开始下载
                                    </button>
                                    <button class="btn btn-danger d-none" id="stopDownload">
                                        <i class="bi bi-stop-fill"></i>
                                        停止下载
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">预计分段数</label>
                                <div class="form-control-plaintext" id="estimatedSegments">-</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 进度显示区域 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-activity"></i>
                            下载进度
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-3" style="height: 25px;">
                            <div class="progress-bar progress-bar-striped" role="progressbar" 
                                 style="width: 0%" id="progressBar">0%</div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>当前状态：</strong>
                                <span id="currentStatus" class="text-muted">等待开始</span>
                            </div>
                            <div class="col-md-3">
                                <strong>已完成段数：</strong>
                                <span id="completedSegments">0</span> / <span id="totalSegments">0</span>
                            </div>
                            <div class="col-md-3">
                                <strong>总记录数：</strong>
                                <span id="totalRecords">0</span>
                            </div>
                            <div class="col-md-3">
                                <strong>预计文件大小：</strong>
                                <span id="estimatedSize">0 MB</span>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="log-container" id="logContainer">
                                <div class="text-muted">等待开始下载...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 字段搜索区域 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-search"></i>
                            字段搜索
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <input type="text" class="form-control" id="fieldSearch" 
                                       placeholder="输入中文字段名，如：股息率、市盈率、总市值...">
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-outline-primary" id="clearSearch">
                                    <i class="bi bi-x-circle"></i>
                                    清除搜索
                                </button>
                            </div>
                        </div>
                        <div class="mt-3" id="searchResults"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格区域 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-table"></i>
                            数据字段说明
                        </h5>
                        <button class="btn btn-outline-secondary btn-sm" id="toggleTable">
                            <i class="bi bi-chevron-down"></i>
                            展开/收起
                        </button>
                    </div>
                    <div class="card-body collapse" id="tableContainer">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="fieldsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>中文名称</th>
                                        <th>英文字段</th>
                                        <th>数据类型</th>
                                        <th>说明</th>
                                        <th>示例值</th>
                                    </tr>
                                </thead>
                                <tbody id="fieldsTableBody">
                                    <!-- 表格内容将通过JavaScript动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 下载历史 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history"></i>
                            下载历史
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>下载时间</th>
                                        <th>日期范围</th>
                                        <th>记录数</th>
                                        <th>文件大小</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="historyTableBody">
                                    <tr>
                                        <td colspan="6" class="text-center text-muted">暂无下载历史</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
