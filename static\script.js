// Tushare Daily Basic 字段映射表
const FIELD_MAPPINGS = {
    '股票代码': { field: 'ts_code', type: 'str', description: '股票代码', example: '000001.SZ' },
    '交易日期': { field: 'trade_date', type: 'str', description: '交易日期', example: '20240101' },
    '收盘价': { field: 'close', type: 'float', description: '当日收盘价', example: '15.23' },
    '换手率': { field: 'turnover_rate', type: 'float', description: '换手率（%）', example: '2.35' },
    '换手率（自由流通股）': { field: 'turnover_rate_f', type: 'float', description: '换手率（自由流通股）（%）', example: '2.45' },
    '成交量比': { field: 'volume_ratio', type: 'float', description: '量比', example: '1.25' },
    '市盈率': { field: 'pe', type: 'float', description: '市盈率（总市值/净利润）', example: '18.5' },
    '市盈率TTM': { field: 'pe_ttm', type: 'float', description: '市盈率（TTM）', example: '19.2' },
    '市净率': { field: 'pb', type: 'float', description: '市净率（总市值/净资产）', example: '1.85' },
    'PS': { field: 'ps', type: 'float', description: '市销率', example: '2.15' },
    'PS TTM': { field: 'ps_ttm', type: 'float', description: '市销率（TTM）', example: '2.25' },
    '股息率': { field: 'dv_ratio', type: 'float', description: '股息率（%）', example: '3.25' },
    'DVT': { field: 'dv_ttm', type: 'float', description: '股息率（TTM）（%）', example: '3.15' },
    '总股本': { field: 'total_share', type: 'float', description: '总股本（万股）', example: '125000' },
    '流通股本': { field: 'float_share', type: 'float', description: '流通股本（万股）', example: '95000' },
    '自由流通股本': { field: 'free_share', type: 'float', description: '自由流通股本（万股）', example: '85000' },
    '总市值': { field: 'total_mv', type: 'float', description: '总市值（万元）', example: '1950000' },
    '流通市值': { field: 'circ_mv', type: 'float', description: '流通市值（万元）', example: '1450000' }
};

// 全局变量
let downloadStatus = {
    isRunning: false,
    currentSegment: 0,
    totalSegments: 0,
    totalRecords: 0,
    progress: 0
};

// Socket.IO连接
const socket = io();

// DOM元素
const elements = {
    startDate: document.getElementById('startDate'),
    endDate: document.getElementById('endDate'),
    startDownload: document.getElementById('startDownload'),
    stopDownload: document.getElementById('stopDownload'),
    estimatedSegments: document.getElementById('estimatedSegments'),
    progressBar: document.getElementById('progressBar'),
    currentStatus: document.getElementById('currentStatus'),
    completedSegments: document.getElementById('completedSegments'),
    totalSegments: document.getElementById('totalSegments'),
    totalRecords: document.getElementById('totalRecords'),
    estimatedSize: document.getElementById('estimatedSize'),
    logContainer: document.getElementById('logContainer'),
    fieldSearch: document.getElementById('fieldSearch'),
    clearSearch: document.getElementById('clearSearch'),
    searchResults: document.getElementById('searchResults'),
    toggleTable: document.getElementById('toggleTable'),
    tableContainer: document.getElementById('tableContainer'),
    fieldsTableBody: document.getElementById('fieldsTableBody'),
    historyTableBody: document.getElementById('historyTableBody')
};

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    setupEventListeners();
    setupSocketListeners();
    loadFieldsTable();
    loadDownloadHistory();
});

// 初始化页面
function initializePage() {
    // 设置默认日期
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
    
    elements.startDate.value = formatDate(lastMonth);
    elements.endDate.value = formatDate(today);
    
    updateEstimatedSegments();
}

// 设置事件监听器
function setupEventListeners() {
    // 日期变化事件
    elements.startDate.addEventListener('change', updateEstimatedSegments);
    elements.endDate.addEventListener('change', updateEstimatedSegments);
    
    // 下载控制按钮
    elements.startDownload.addEventListener('click', startDownload);
    elements.stopDownload.addEventListener('click', stopDownload);
    
    // 字段搜索
    elements.fieldSearch.addEventListener('input', handleFieldSearch);
    elements.clearSearch.addEventListener('click', clearSearch);
    
    // 表格展开/收起
    elements.toggleTable.addEventListener('click', toggleTable);
}

// 设置Socket.IO事件监听器
function setupSocketListeners() {
    // 连接成功
    socket.on('connect', function() {
        console.log('Connected to server');
    });
    
    // 下载状态更新
    socket.on('download_status', function(status) {
        updateDownloadStatus(status);
    });
    
    // 下载日志
    socket.on('download_log', function(log) {
        addLogEntry(log.message, log.type);
    });
    
    // 连接断开
    socket.on('disconnect', function() {
        console.log('Disconnected from server');
        elements.currentStatus.innerHTML = '<span class="status-indicator error"></span>连接断开';
    });
}

// 格式化日期
function formatDate(date) {
    return date.toISOString().split('T')[0];
}

// 更新预计分段数
function updateEstimatedSegments() {
    const startDate = new Date(elements.startDate.value);
    const endDate = new Date(elements.endDate.value);
    
    if (startDate && endDate && startDate <= endDate) {
        const diffTime = Math.abs(endDate - startDate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
        const segments = Math.ceil(diffDays / 30);
        
        elements.estimatedSegments.textContent = segments;
    } else {
        elements.estimatedSegments.textContent = '-';
    }
}

// 开始下载
function startDownload() {
    if (!validateDates()) {
        return;
    }
    
    // 发送下载请求到服务器
    fetch('/api/start_download', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            start_date: elements.startDate.value,
            end_date: elements.endDate.value
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('下载失败: ' + data.error);
        } else {
            // 更新UI
            elements.startDownload.classList.add('d-none');
            elements.stopDownload.classList.remove('d-none');
            elements.currentStatus.innerHTML = '<span class="status-indicator running"></span>正在下载...';
            
            // 清空日志
            elements.logContainer.innerHTML = '';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('下载请求失败: ' + error.message);
    });
}

// 停止下载
function stopDownload() {
    fetch('/api/stop_download', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        // 更新UI
        elements.startDownload.classList.remove('d-none');
        elements.stopDownload.classList.add('d-none');
        elements.currentStatus.innerHTML = '<span class="status-indicator error"></span>已停止';
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// 验证日期
function validateDates() {
    const startDate = new Date(elements.startDate.value);
    const endDate = new Date(elements.endDate.value);
    
    if (!elements.startDate.value || !elements.endDate.value) {
        alert('请选择开始日期和结束日期');
        return false;
    }
    
    if (startDate > endDate) {
        alert('开始日期不能晚于结束日期');
        return false;
    }
    
    return true;
}

// 更新下载状态
function updateDownloadStatus(status) {
    downloadStatus = status;
    
    // 更新进度条
    elements.progressBar.style.width = status.progress + '%';
    elements.progressBar.textContent = Math.round(status.progress) + '%';
    
    // 更新状态信息
    elements.completedSegments.textContent = status.current_segment;
    elements.totalSegments.textContent = status.total_segments;
    elements.totalRecords.textContent = status.total_records.toLocaleString();
    elements.estimatedSize.textContent = (status.total_records * 0.0001).toFixed(2) + ' MB';
    
    // 更新状态指示器
    let statusText = '';
    let statusClass = '';
    
    switch (status.status) {
        case 'waiting':
            statusText = '等待开始';
            statusClass = 'waiting';
            break;
        case 'starting':
            statusText = '正在启动...';
            statusClass = 'running';
            break;
        case 'downloading':
            statusText = '正在下载...';
            statusClass = 'running';
            break;
        case 'completed':
            statusText = '下载完成';
            statusClass = 'success';
            elements.startDownload.classList.remove('d-none');
            elements.stopDownload.classList.add('d-none');
            loadDownloadHistory(); // 重新加载下载历史
            break;
        case 'stopped':
            statusText = '已停止';
            statusClass = 'error';
            elements.startDownload.classList.remove('d-none');
            elements.stopDownload.classList.add('d-none');
            break;
        case 'error':
            statusText = '下载失败';
            statusClass = 'error';
            elements.startDownload.classList.remove('d-none');
            elements.stopDownload.classList.add('d-none');
            break;
    }
    
    elements.currentStatus.innerHTML = `<span class="status-indicator ${statusClass}"></span>${statusText}`;
}

// 添加日志条目
function addLogEntry(message, type = 'info') {
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
    
    elements.logContainer.appendChild(logEntry);
    elements.logContainer.scrollTop = elements.logContainer.scrollHeight;
}

// 处理字段搜索
function handleFieldSearch() {
    const searchTerm = elements.fieldSearch.value.trim().toLowerCase();
    
    if (searchTerm === '') {
        elements.searchResults.innerHTML = '';
        return;
    }
    
    const results = [];
    
    // 搜索匹配的字段
    for (const [chineseName, fieldInfo] of Object.entries(FIELD_MAPPINGS)) {
        if (chineseName.toLowerCase().includes(searchTerm) || 
            fieldInfo.field.toLowerCase().includes(searchTerm) ||
            fieldInfo.description.toLowerCase().includes(searchTerm)) {
            results.push({ chineseName, ...fieldInfo });
        }
    }
    
    displaySearchResults(results, searchTerm);
}

// 显示搜索结果
function displaySearchResults(results, searchTerm) {
    if (results.length === 0) {
        elements.searchResults.innerHTML = '<div class="text-muted">未找到匹配的字段</div>';
        return;
    }

    const resultsHtml = results.map(result => `
        <div class="search-result-item fade-in">
            <div class="field-name">${highlightText(result.chineseName, searchTerm)}</div>
            <div class="mt-1">
                <span class="field-code">${result.field}</span>
                <span class="badge bg-secondary ms-2">${result.type}</span>
            </div>
            <div class="field-description">${highlightText(result.description, searchTerm)}</div>
            <div class="text-muted mt-1">示例值: ${result.example}</div>
        </div>
    `).join('');

    elements.searchResults.innerHTML = resultsHtml;
}

// 高亮搜索文本
function highlightText(text, searchTerm) {
    if (!searchTerm) return text;

    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<span class="highlight">$1</span>');
}

// 清除搜索
function clearSearch() {
    elements.fieldSearch.value = '';
    elements.searchResults.innerHTML = '';
}

// 切换表格显示
function toggleTable() {
    const tableContainer = new bootstrap.Collapse(elements.tableContainer);
    tableContainer.toggle();

    const icon = elements.toggleTable.querySelector('i');
    if (elements.tableContainer.classList.contains('show')) {
        icon.className = 'bi bi-chevron-up';
    } else {
        icon.className = 'bi bi-chevron-down';
    }
}

// 加载字段表格
function loadFieldsTable() {
    const tableRows = Object.entries(FIELD_MAPPINGS).map(([chineseName, fieldInfo]) => `
        <tr>
            <td><strong>${chineseName}</strong></td>
            <td><code>${fieldInfo.field}</code></td>
            <td><span class="badge bg-info">${fieldInfo.type}</span></td>
            <td>${fieldInfo.description}</td>
            <td><code>${fieldInfo.example}</code></td>
        </tr>
    `).join('');

    elements.fieldsTableBody.innerHTML = tableRows;
}

// 加载下载历史
function loadDownloadHistory() {
    fetch('/api/download_history')
    .then(response => response.json())
    .then(history => {
        if (history.length === 0) {
            elements.historyTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">暂无下载历史</td></tr>';
            return;
        }

        const historyRows = history.map(record => {
            const downloadTime = new Date(record.download_time).toLocaleString();
            const dateRange = `${record.start_date} 至 ${record.end_date}`;
            const recordCount = record.record_count.toLocaleString();
            const fileSize = (record.file_size / 1024 / 1024).toFixed(2) + ' MB';
            const status = record.status === 'completed' ?
                '<span class="badge bg-success">完成</span>' :
                '<span class="badge bg-danger">失败</span>';

            return `
                <tr>
                    <td>${downloadTime}</td>
                    <td>${dateRange}</td>
                    <td>${recordCount}</td>
                    <td>${fileSize}</td>
                    <td>${status}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="downloadFile('${record.filename}')">
                            <i class="bi bi-download"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');

        elements.historyTableBody.innerHTML = historyRows;
    })
    .catch(error => {
        console.error('Error loading download history:', error);
    });
}

// 下载文件
function downloadFile(filename) {
    window.open(`/api/download_file/${filename}`, '_blank');
}
