import tushare as ts
import pandas as pd
from datetime import datetime, timedelta
import time
import os

# 设置您的tushare token
ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')

# 初始化pro接口
pro = ts.pro_api()

def get_valid_date(prompt):
    """获取并验证用户输入的日期格式"""
    while True:
        date_str = input(prompt)
        try:
            # 验证日期格式是否正确
            datetime.strptime(date_str, '%Y%m%d')
            return date_str
        except ValueError:
            print("日期格式错误！请输入YYYYMMDD格式，例如：20240101")

def split_date_range(start_date, end_date, days_per_chunk=30):
    """将日期范围分割成小段，避免offset限制"""
    start_dt = datetime.strptime(start_date, '%Y%m%d')
    end_dt = datetime.strptime(end_date, '%Y%m%d')

    date_ranges = []
    current_start = start_dt

    while current_start <= end_dt:
        current_end = min(current_start + timedelta(days=days_per_chunk-1), end_dt)
        date_ranges.append((
            current_start.strftime('%Y%m%d'),
            current_end.strftime('%Y%m%d')
        ))
        current_start = current_end + timedelta(days=1)

    return date_ranges

def download_daily_basic_segment(start_date, end_date, segment_info=""):
    """下载指定日期范围的daily_basic数据"""
    df = None
    page = 1
    max_offset = 99000  # 设置安全的最大offset，避免超过100000限制

    print(f"正在下载{segment_info} ({start_date} 至 {end_date})")

    while True:
        try:
            current_offset = (page-1) * 1000
            if current_offset >= max_offset:
                print(f"  达到offset限制，当前段数据获取完成")
                break

            print(f"  第{page}页...", end=" ")

            temp_df = pro.daily_basic(
                ts_code='',
                start_date=start_date,
                end_date=end_date,
                limit=1000,
                offset=current_offset
            )

            if temp_df.empty:
                print("完成")
                break

            if df is None:
                df = temp_df
            else:
                df = pd.concat([df, temp_df], ignore_index=True)

            print(f"成功，累计{len(df)}条")
            page += 1
            time.sleep(0.3)  # 稍微减少延迟

        except Exception as e:
            if "offset不能大于" in str(e):
                print(f"  达到offset限制，当前段数据获取完成")
                break
            else:
                print(f"  获取失败：{e}")
                raise e

    return df if df is not None else pd.DataFrame()

# 获取用户输入的时间范围
print("=" * 50)
print("Tushare Daily Basic 数据下载工具 (智能分段版)")
print("=" * 50)
print("请输入下载的时间范围（YYYYMMDD格式）")
print("示例：20240101 表示2024年1月1日")
print("注意：大时间范围会自动分段下载，避免API限制")
print("-" * 30)

start_date = get_valid_date("开始日期: ")
end_date = get_valid_date("结束日期: ")

# 验证时间范围有效性
if start_date > end_date:
    print("错误：开始日期不能晚于结束日期！")
    exit(1)

# 计算日期范围和预估分段数
start_dt = datetime.strptime(start_date, '%Y%m%d')
end_dt = datetime.strptime(end_date, '%Y%m%d')
total_days = (end_dt - start_dt).days + 1
estimated_segments = (total_days + 29) // 30  # 每30天一段

# 确认下载信息
print(f"\n确认下载信息：")
print(f"开始日期：{start_date}")
print(f"结束日期：{end_date}")
print(f"时间跨度：{total_days} 天")
print(f"预计分段：{estimated_segments} 段")
confirm = input("确认下载？(y/n): ").lower().strip()
if confirm != 'y' and confirm != 'yes':
    print("下载已取消")
    exit(0)

# 分段下载数据
print(f"\n开始分段下载数据...")
date_ranges = split_date_range(start_date, end_date, days_per_chunk=30)
all_data = []

print(f"总共需要下载 {len(date_ranges)} 个时间段")
print("=" * 50)

for i, (seg_start, seg_end) in enumerate(date_ranges, 1):
    try:
        segment_info = f"第{i}/{len(date_ranges)}段"
        segment_df = download_daily_basic_segment(seg_start, seg_end, segment_info)

        if not segment_df.empty:
            all_data.append(segment_df)
            print(f"  ✓ {segment_info} 完成，获得 {len(segment_df)} 条记录")
        else:
            print(f"  - {segment_info} 无数据")

        # 段间休息，避免请求过于频繁
        if i < len(date_ranges):
            time.sleep(1)

    except Exception as e:
        print(f"  ✗ {segment_info} 失败：{e}")
        print("请检查网络连接和tushare token是否有效")
        exit(1)

# 合并所有数据
if all_data:
    print(f"\n正在合并 {len(all_data)} 个数据段...")
    df = pd.concat(all_data, ignore_index=True)
    print(f"合并完成，总计 {len(df)} 条记录")
else:
    df = None

if df is None or len(df) == 0:
    print("\n没有获取到任何数据，请检查日期范围是否正确")
    print("可能的原因：")
    print("1. 日期范围内没有交易日")
    print("2. 日期格式错误")
    print("3. tushare接口限制")
    exit(1)

# 生成包含时间范围的文件名
filename = f'daily_basic_{start_date}_{end_date}.csv'

try:
    # 保存数据到CSV文件
    df.to_csv(filename, index=False, encoding='utf-8-sig')

    # 获取文件大小
    file_size = os.path.getsize(filename) / 1024 / 1024  # MB

    print(f"\n" + "=" * 50)
    print("下载完成！")
    print("=" * 50)
    print(f"数据记录数：{len(df):,} 条")
    print(f"文件名称：{filename}")
    print(f"文件大小：{file_size:.2f} MB")
    print(f"保存路径：{os.path.abspath(filename)}")
    print("=" * 50)

except Exception as e:
    print(f"\n保存文件失败：{e}")
    exit(1)