import tushare as ts
from datetime import datetime
import time

# 设置您的tushare token
ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')

# 初始化pro接口
pro = ts.pro_api()

def get_valid_date(prompt):
    """获取并验证用户输入的日期格式"""
    while True:
        date_str = input(prompt)
        try:
            # 验证日期格式是否正确
            datetime.strptime(date_str, '%Y%m%d')
            return date_str
        except ValueError:
            print("日期格式错误！请输入YYYYMMDD格式，例如：20240101")

# 获取用户输入的时间范围
print("请输入下载的时间范围（YYYYMMDD格式）")
start_date = get_valid_date("开始日期: ")
end_date = get_valid_date("结束日期: ")

# 验证时间范围有效性
if start_date > end_date:
    print("错误：开始日期不能晚于结束日期！")
    exit(1)

# 分页获取所有数据
df = None
page = 1
print(f"正在分页下载{start_date}至{end_date}的完整数据...")

while True:
    try:
        # 每次获取1000条记录，设置分页参数
        temp_df = pro.daily_basic(
            ts_code='',
            start_date=start_date,
            end_date=end_date,
            limit=1000,
            offset=(page-1)*1000
        )

        if temp_df.empty:
            break  # 没有更多数据时退出循环

        # 合并数据
        if df is None:
            df = temp_df
        else:
            # 使用pd.concat替代append方法（Pandas 2.0+兼容）
            df = pd.concat([df, temp_df], ignore_index=True)

        print(f"已获取{len(df)}条记录...")
        page += 1
        time.sleep(0.5)  # 避免请求过于频繁

    except Exception as e:
        print(f"获取数据失败：{e}")
        exit(1)

if df is None or len(df) == 0:
    print("没有获取到任何数据，请检查日期范围是否正确")
    exit(1)

# 生成包含时间范围的文件名
filename = f'daily_basic_{start_date}_{end_date}.csv'
df.to_csv(filename, index=False)

print(f'数据下载完成，共{len(df)}条记录，已保存至{filename}')