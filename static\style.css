/* 自定义样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.3rem;
}

.navbar-brand i {
    margin-right: 8px;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 10px;
    margin-bottom: 20px;
}

.card-header {
    background-color: #fff;
    border-bottom: 2px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
    padding: 15px 20px;
}

.card-title i {
    margin-right: 8px;
    color: #0d6efd;
}

/* 进度条样式 */
.progress {
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    font-weight: bold;
    transition: width 0.3s ease;
}

.progress-bar.progress-bar-striped {
    background-image: linear-gradient(45deg, rgba(255,255,255,.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,.15) 50%, rgba(255,255,255,.15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}

/* 日志容器样式 */
.log-container {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    max-height: 200px;
    overflow-y: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
}

.log-entry.success {
    color: #198754;
}

.log-entry.error {
    color: #dc3545;
}

.log-entry.info {
    color: #0d6efd;
}

.log-entry.warning {
    color: #fd7e14;
}

/* 搜索结果样式 */
#searchResults {
    min-height: 50px;
}

.search-result-item {
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.search-result-item:hover {
    background-color: #d1ecf1;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.search-result-item .field-name {
    font-weight: bold;
    color: #0d6efd;
    font-size: 1.1rem;
}

.search-result-item .field-code {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    color: #6f42c1;
    font-weight: bold;
}

.search-result-item .field-description {
    color: #6c757d;
    margin-top: 5px;
}

/* 表格样式 */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

.table th {
    background-color: #495057;
    color: white;
    font-weight: 600;
    border: none;
    padding: 12px;
}

.table td {
    padding: 10px 12px;
    vertical-align: middle;
    border-color: #e9ecef;
}

.table-hover tbody tr:hover {
    background-color: #f8f9fa;
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn i {
    margin-right: 5px;
}

/* 表单控件样式 */
.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 10px 12px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.waiting {
    background-color: #6c757d;
}

.status-indicator.running {
    background-color: #0d6efd;
    animation: pulse 1.5s infinite;
}

.status-indicator.success {
    background-color: #198754;
}

.status-indicator.error {
    background-color: #dc3545;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* 滚动条样式 */
.log-container::-webkit-scrollbar {
    width: 8px;
}

.log-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 高亮搜索结果 */
.highlight {
    background-color: #fff3cd;
    padding: 1px 3px;
    border-radius: 3px;
    font-weight: bold;
}

/* 工具提示样式 */
.tooltip-inner {
    background-color: #212529;
    color: white;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 0.875rem;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
