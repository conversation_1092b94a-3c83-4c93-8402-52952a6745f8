# Tushare Daily Basic 数据下载工具

一个功能完整的Web应用程序，用于下载和管理Tushare Daily Basic数据。

## 功能特点

### 🚀 核心功能
- **智能分段下载** - 自动处理大时间范围，避免API限制
- **实时进度显示** - WebSocket实时更新下载进度
- **字段搜索** - 中文搜索对应的英文字段名
- **数据表格** - 完整的字段说明和示例
- **下载历史** - 记录和管理历史下载

### 🎯 界面功能
- **日期选择** - 灵活选择下载时间范围
- **进度监控** - 实时显示下载状态和进度
- **字段查询** - 搜索"股息率"等中文名称获得对应字段
- **表格展示** - 中英文对照的完整字段说明
- **历史管理** - 查看和下载历史文件

### 🔗 外部链接
- **Tushare官网** - 直接跳转到官方网站
- **API文档** - 快速访问Daily Basic接口文档

## 安装和运行

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置Tushare Token
在 `app.py` 文件中修改你的tushare token：
```python
ts.set_token('your-tushare-token-here')
```

### 3. 运行应用
```bash
python app.py
```

### 4. 访问应用
打开浏览器访问：http://localhost:5000

## 文件结构

```
├── app.py                 # Flask后端应用
├── download_daily_basic.py # 命令行下载脚本
├── requirements.txt       # Python依赖
├── templates/
│   └── index.html        # 主页面模板
├── static/
│   ├── style.css         # 样式文件
│   └── script.js         # JavaScript功能
└── README.md             # 说明文档
```

## 使用说明

### 下载数据
1. 选择开始日期和结束日期
2. 系统会自动计算预计分段数
3. 点击"开始下载"按钮
4. 实时查看下载进度和日志
5. 下载完成后可在历史记录中查看和下载文件

### 字段搜索
1. 在搜索框中输入中文字段名，如"股息率"
2. 系统会显示对应的英文字段名和详细信息
3. 支持模糊搜索和高亮显示

### 查看字段说明
1. 点击"数据字段说明"区域的"展开/收起"按钮
2. 查看完整的字段对照表
3. 包含中文名称、英文字段、数据类型、说明和示例值

## 技术特点

### 前端技术
- **Bootstrap 5** - 响应式UI框架
- **Socket.IO** - 实时通信
- **Bootstrap Icons** - 图标库
- **原生JavaScript** - 无额外框架依赖

### 后端技术
- **Flask** - Web框架
- **Flask-SocketIO** - WebSocket支持
- **Tushare** - 数据源API
- **Pandas** - 数据处理

### 核心算法
- **智能分段** - 自动将大时间范围分割成30天段
- **错误恢复** - 处理API限制和网络错误
- **实时通信** - WebSocket推送进度和日志

## 字段映射

支持以下字段的中英文搜索：

| 中文名称 | 英文字段 | 说明 |
|---------|---------|------|
| 股票代码 | ts_code | 股票代码 |
| 交易日期 | trade_date | 交易日期 |
| 收盘价 | close | 当日收盘价 |
| 换手率 | turnover_rate | 换手率（%） |
| 市盈率 | pe | 市盈率（总市值/净利润） |
| 市净率 | pb | 市净率（总市值/净资产） |
| 股息率 | dv_ratio | 股息率（%） |
| 总市值 | total_mv | 总市值（万元） |
| 流通市值 | circ_mv | 流通市值（万元） |
| ... | ... | 更多字段请在应用中查看 |

## 注意事项

1. **Token配置** - 请确保配置有效的tushare token
2. **网络连接** - 需要稳定的网络连接
3. **API限制** - 遵守tushare的API调用限制
4. **存储空间** - 大时间范围的数据可能占用较多磁盘空间

## 故障排除

### 常见问题
1. **连接失败** - 检查网络连接和token配置
2. **下载中断** - 可以重新开始下载，系统会自动分段处理
3. **文件过大** - 考虑缩小时间范围或分批下载

### 日志查看
- 应用会在控制台输出详细日志
- 前端页面也会显示实时下载日志
- 检查浏览器开发者工具的控制台信息

## 许可证

本项目仅供学习和研究使用，请遵守tushare的使用条款。
